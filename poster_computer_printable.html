<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduCasheer SiliconSpark - Computer Education Programs</title>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif;
        }

        body {
            background: #1a2980;
            color: #333;
            min-height: 100vh;
            overflow-x: auto;
            position: relative;
            padding: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        /*
         * Form-based poster for EduCasheer SiliconSpark
         * Optimized for 4-page A4 printing with better colors
         */
        .poster-container {
            position: relative;
            background: #ffffff;
            width: 210mm;
            max-width: 100%;
            margin: 0 auto;
            overflow: visible;
            box-sizing: border-box;
            border-radius: 0;
            box-shadow: none;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            padding: 20mm;
            box-sizing: border-box;
            page-break-after: always;
            background: #ffffff;
            border: 2px solid #4568dc;
            margin-bottom: 10px;
        }

        .page:last-child {
            page-break-after: auto;
        }

        .poster-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /* Simplified pattern for better printing */
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="rgba(69,104,220,0.08)" stroke-width="1.5"/></svg>');
            opacity: 0.2;
            pointer-events: none;
            z-index: -1;
        }

        /* Decorative elements for the poster - optimized for print */
        .poster-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            border-radius: 20px;
            box-shadow: inset 0 0 80px rgba(69, 104, 220, 0.2);
        }

        .poster-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(69, 104, 220, 0.05) 0%, rgba(176, 106, 179, 0.05) 100%);
            pointer-events: none;
            z-index: 2;
        }

        /* Page Header */
        .page-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4568dc;
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -20mm -20mm 30px -20mm;
            box-shadow: 0 5px 15px rgba(69, 104, 220, 0.3);
        }

        .logo-container {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .logo-img {
            width: 80px;
            height: 80px;
            margin-right: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .main-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle, .page-subtitle {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* Content Sections */
        .content-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #4568dc;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #4568dc;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            font-size: 22px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 5px 15px rgba(69, 104, 220, 0.3);
        }

        .highlight-box h3 {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .highlight-box p {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Program Grid */
        .programs-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .program-card {
            background: white;
            border: 2px solid #4568dc;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 3px 10px rgba(69, 104, 220, 0.1);
            transition: transform 0.3s ease;
        }

        .program-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(69, 104, 220, 0.2);
        }

        .program-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .program-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 10px;
            font-size: 16px;
        }

        .program-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .program-description {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* Contact Information */
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4568dc;
        }

        .contact-item i {
            color: #4568dc;
            margin-right: 10px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        .contact-text {
            font-size: 14px;
            color: #333;
        }

        /* QR Code Section */
        .qr-section {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code-container {
            display: inline-block;
            padding: 15px;
            background: white;
            border: 3px solid #4568dc;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(69, 104, 220, 0.2);
        }

        .qr-code-container img {
            width: 120px;
            height: 120px;
            border-radius: 5px;
        }

        .qr-text {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }

        /* Fee Structure Table */
        .fee-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .fee-table th {
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .fee-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .fee-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .fee-table tr:hover {
            background: #e3f2fd;
        }

        /* Form Styles */
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #4568dc;
            margin: 20px 0;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }

        /* Form line styling for print */
        .form-line {
            border-bottom: 2px solid #333;
            height: 30px;
            margin-top: 5px;
            width: 100%;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .checkbox-group label {
            font-size: 14px;
            color: #333;
        }

        /* Print Button */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 50px;
            cursor: pointer;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .print-button:hover {
            background: linear-gradient(135deg, #5B86E5 0%, #b06ab3 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
        }

        .print-button i {
            font-size: 16px;
        }

        /* Page setup for A4 printing */
        @page {
            size: A4;
            margin: 15mm;
        }

        /* Print Styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-button {
                display: none !important;
            }

            body {
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 12pt !important;
                line-height: 1.3 !important;
            }

            .poster-container {
                box-shadow: none !important;
                border: none !important;
                width: 100% !important;
                max-width: none !important;
            }

            .page {
                width: 100% !important;
                min-height: auto !important;
                padding: 15mm !important;
                margin: 0 !important;
                border: 2px solid #4568dc !important;
                page-break-after: always !important;
                box-sizing: border-box !important;
            }

            .page:last-child {
                page-break-after: auto !important;
            }

            .page-header {
                margin: -15mm -15mm 15mm -15mm !important;
                padding: 15mm !important;
                background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%) !important;
                color: white !important;
                border-radius: 0 !important;
            }

            .highlight-box {
                background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%) !important;
                color: white !important;
                border: 1px solid #4568dc !important;
            }

            .program-number {
                background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%) !important;
                color: white !important;
            }

            .fee-table th {
                background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%) !important;
                color: white !important;
            }

            .instructor-highlight {
                background: linear-gradient(135deg, #26d0ce 0%, #1a2980 100%) !important;
                color: white !important;
            }

            .form-line {
                border-bottom: 2px solid #333 !important;
                height: 25pt !important;
                margin-top: 3pt !important;
                width: 100% !important;
            }

            .form-label {
                font-weight: bold !important;
                font-size: 11pt !important;
                color: #333 !important;
                margin-bottom: 3pt !important;
            }

            .form-section {
                border: 2px solid #4568dc !important;
                background: #f8f9fa !important;
                margin-bottom: 15pt !important;
                page-break-inside: avoid !important;
            }

            .content-section {
                border: 1px solid #4568dc !important;
                background: #f8f9fa !important;
                page-break-inside: avoid !important;
            }

            .section-title {
                color: #4568dc !important;
                font-size: 14pt !important;
                font-weight: bold !important;
            }

            .checkbox-group {
                margin: 8pt 0 !important;
            }

            .checkbox-group input[type="checkbox"],
            .checkbox-group input[type="radio"] {
                width: 15pt !important;
                height: 15pt !important;
                margin-right: 8pt !important;
            }

            .checkbox-group label {
                font-size: 11pt !important;
                line-height: 1.2 !important;
            }

            .programs-grid {
                display: block !important;
            }

            .program-card {
                display: block !important;
                margin-bottom: 10pt !important;
                border: 1px solid #4568dc !important;
                page-break-inside: avoid !important;
            }

            .contact-grid {
                display: block !important;
            }

            .contact-item {
                display: block !important;
                margin-bottom: 8pt !important;
                border-left: 3px solid #4568dc !important;
            }

            .benefits-grid {
                display: block !important;
            }

            .benefit-item {
                display: block !important;
                margin-bottom: 8pt !important;
                border-left: 3px solid #4568dc !important;
            }

            .fee-table {
                width: 100% !important;
                border-collapse: collapse !important;
                font-size: 10pt !important;
            }

            .fee-table td, .fee-table th {
                border: 1px solid #333 !important;
                padding: 6pt !important;
            }

            .qr-code-container {
                border: 2px solid #4568dc !important;
                background: white !important;
            }

            .qr-code-container img {
                width: 80pt !important;
                height: 80pt !important;
            }

            /* Hide interactive elements in print */
            script {
                display: none !important;
            }

            /* Ensure signature lines are visible */
            div[style*="border-bottom"] {
                border-bottom: 2px solid #333 !important;
                height: 30pt !important;
            }
        }

        /* List Styles */
        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .feature-list li:before {
            content: "✓";
            color: #4568dc;
            font-weight: bold;
            margin-right: 10px;
            font-size: 16px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        /* Instructor Highlight */
        .instructor-highlight {
            background: linear-gradient(135deg, #26d0ce 0%, #1a2980 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
        }

        .instructor-highlight h4 {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .instructor-highlight p {
            font-size: 12px;
            opacity: 0.9;
        }

        /* Benefits Grid */
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .benefit-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4568dc;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .benefit-item h4 {
            color: #4568dc;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .benefit-item p {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Print Button -->
    <button class="print-button" onclick="window.print()">
        <i class="fas fa-print"></i> Print Form
    </button>

    <div class="poster-container">
        <div class="container">
            <!-- PAGE 1: Header, Contact Info, and Program Overview -->
            <div class="page">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="logo.png" alt="EduCasheer SiliconSpark Logo" class="logo-img">
                        <div>
                            <h1 class="main-title">EDUCASHEER SILICONSPARK</h1>
                            <p class="subtitle">School of Computing and Technology</p>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3><i class="fas fa-rocket" style="margin-right: 10px;"></i>Transform Your Future with Technology</h3>
                    <p>Join our state-of-the-art computer lab and launch your career in the digital world!</p>
                </div>

                <div class="instructor-highlight">
                    <h4><i class="fas fa-user-graduate" style="margin-right: 8px;"></i>Led by Musavir Khaliq</h4>
                    <p>Computer Science Researcher with Master's from IISc | Industry Expert</p>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-laptop-code"></i>Our Programs</h2>
                    <div class="programs-grid">
                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">01</div>
                                <div class="program-title">Learn from Scratch</div>
                            </div>
                            <div class="program-description">
                                Perfect for absolute beginners. Master basic computer operations and digital literacy skills.
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">02</div>
                                <div class="program-title">Learn to Code</div>
                            </div>
                            <div class="program-description">
                                In-depth courses in Python, JavaScript, C, C++ and ethical hacking. Build real-world projects.
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">03</div>
                                <div class="program-title">Build the Web</div>
                            </div>
                            <div class="program-description">
                                Create dynamic websites and mobile apps. Master React, Angular, and Vue frameworks.
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">04</div>
                                <div class="program-title">Explore AI & Beyond</div>
                            </div>
                            <div class="program-description">
                                Dive into AI, Machine Learning, and Deep Learning. Build intelligent systems for the future.
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">05</div>
                                <div class="program-title">Computer Hardware</div>
                            </div>
                            <div class="program-description">
                                Hands-on workshops to assemble PCs, create drones, and develop IoT devices.
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number">06</div>
                                <div class="program-title">Career Counseling</div>
                            </div>
                            <div class="program-description">
                                Get personalized guidance from industry leaders and build professional networks.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-address-book"></i>Contact Information</h2>
                    <div class="contact-grid">
                        <div class="contact-item">
                            <i class="fas fa-phone-alt"></i>
                            <div class="contact-text">+91 8825063816<br>+91 7789681275<br>+91 8082775767</div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div class="contact-text"><EMAIL></div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-globe"></i>
                            <div class="contact-text">www.educasheer.in</div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div class="contact-text">New Colony, Jahangeerpore<br>Shrakwara, Baramulla Kashmir</div>
                        </div>
                    </div>
                </div>

                <div class="qr-section">
                    <div class="qr-code-container">
                        <img src="QRcode.png" alt="QR Code for More Information">
                    </div>
                    <p class="qr-text">Scan for detailed program information and enrollment</p>
                </div>
            </div>

            <!-- PAGE 2: Detailed Program Descriptions and Benefits -->
            <div class="page">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="logo.png" alt="EduCasheer SiliconSpark Logo" class="logo-img">
                        <div>
                            <h1 class="page-title">Program Details & Benefits</h1>
                            <p class="page-subtitle">Comprehensive technology education for all skill levels</p>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-chalkboard-teacher"></i>Learn from Scratch</h2>
                    <ul class="feature-list">
                        <li>Introduction to computers for absolute beginners</li>
                        <li>Master basic operations and essential digital literacy skills</li>
                        <li>Learn file management and organization techniques</li>
                        <li>Understanding operating systems (Windows, Linux, macOS)</li>
                        <li>Internet basics and online safety</li>
                    </ul>
                    <div class="highlight-box">
                        <h3>Key Benefit</h3>
                        <p>No prior experience needed - start your tech journey today!</p>
                    </div>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-code"></i>Learn to Code</h2>
                    <ul class="feature-list">
                        <li>In-depth courses in Python, JavaScript, C, C++ and more</li>
                        <li>Master ethical hacking to protect and secure digital systems</li>
                        <li>Build real-world projects for your portfolio</li>
                        <li>Version control with Git and GitHub</li>
                        <li>Database management and SQL</li>
                        <li>Software development best practices</li>
                    </ul>
                    <div class="highlight-box">
                        <h3>Key Benefit</h3>
                        <p>Become job-ready with industry-relevant programming skills</p>
                    </div>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-globe"></i>Build the Web</h2>
                    <ul class="feature-list">
                        <li>Create dynamic, user-friendly websites from scratch</li>
                        <li>Develop innovative mobile and desktop applications</li>
                        <li>Master modern frameworks like React, Angular and Vue</li>
                        <li>Backend development with Node.js and Express</li>
                        <li>Responsive design and user experience (UX)</li>
                        <li>E-commerce and content management systems</li>
                    </ul>
                    <div class="highlight-box">
                        <h3>Key Benefit</h3>
                        <p>Launch your career as a full-stack web developer</p>
                    </div>
                </div>

                <div class="benefits-grid">
                    <div class="benefit-item">
                        <h4><i class="fas fa-certificate"></i> Industry Certification</h4>
                        <p>Receive recognized certificates upon course completion</p>
                    </div>
                    <div class="benefit-item">
                        <h4><i class="fas fa-users"></i> Small Class Sizes</h4>
                        <p>Personalized attention with maximum 10 students per batch</p>
                    </div>
                    <div class="benefit-item">
                        <h4><i class="fas fa-laptop"></i> Modern Equipment</h4>
                        <p>Access to latest computers and software tools</p>
                    </div>
                    <div class="benefit-item">
                        <h4><i class="fas fa-clock"></i> Flexible Timings</h4>
                        <p>Morning, afternoon, and evening batches available</p>
                    </div>
                </div>
            </div>

            <!-- PAGE 3: Fee Structure and Course Plans -->
            <div class="page">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="logo.png" alt="EduCasheer SiliconSpark Logo" class="logo-img">
                        <div>
                            <h1 class="page-title">Fee Structure & Course Plans</h1>
                            <p class="page-subtitle">Affordable pricing for quality tech education</p>
                        </div>
                    </div>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-money-bill-wave"></i>Course Fees</h2>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Duration</th>
                                <th>Fee (Monthly)</th>
                                <th>Total Fee</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Learn from Scratch</strong></td>
                                <td>2 Months</td>
                                <td>₹1,500</td>
                                <td>₹3,000</td>
                            </tr>
                            <tr>
                                <td><strong>Learn to Code (Basic)</strong></td>
                                <td>3 Months</td>
                                <td>₹2,000</td>
                                <td>₹6,000</td>
                            </tr>
                            <tr>
                                <td><strong>Learn to Code (Advanced)</strong></td>
                                <td>4 Months</td>
                                <td>₹2,500</td>
                                <td>₹10,000</td>
                            </tr>
                            <tr>
                                <td><strong>Build the Web</strong></td>
                                <td>4 Months</td>
                                <td>₹3,000</td>
                                <td>₹12,000</td>
                            </tr>
                            <tr>
                                <td><strong>Explore AI & Beyond</strong></td>
                                <td>5 Months</td>
                                <td>₹3,500</td>
                                <td>₹17,500</td>
                            </tr>
                            <tr>
                                <td><strong>Computer Hardware</strong></td>
                                <td>3 Months</td>
                                <td>₹2,200</td>
                                <td>₹6,600</td>
                            </tr>
                            <tr>
                                <td><strong>Complete Package</strong></td>
                                <td>12 Months</td>
                                <td>₹4,000</td>
                                <td>₹48,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-brain"></i>Advanced Programs</h2>
                    <div class="programs-grid">
                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number"><i class="fas fa-robot"></i></div>
                                <div class="program-title">AI & Machine Learning</div>
                            </div>
                            <div class="program-description">
                                • Python for Data Science<br>
                                • TensorFlow and PyTorch<br>
                                • Neural Networks & Deep Learning<br>
                                • Computer Vision & NLP
                            </div>
                        </div>

                        <div class="program-card">
                            <div class="program-header">
                                <div class="program-number"><i class="fas fa-microchip"></i></div>
                                <div class="program-title">Hardware & IoT</div>
                            </div>
                            <div class="program-description">
                                • PC Assembly & Troubleshooting<br>
                                • Arduino & Raspberry Pi<br>
                                • Drone Development<br>
                                • Smart Home Systems
                            </div>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3><i class="fas fa-gift"></i> Special Offers</h3>
                    <p>• 10% discount for students<br>
                    • 15% discount for complete package enrollment<br>
                    • Free career counseling session with every course<br>
                    • Flexible payment plans available</p>
                </div>

                <div class="content-section">
                    <h2 class="section-title"><i class="fas fa-calendar-alt"></i>Batch Timings</h2>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <h4>Morning Batch</h4>
                            <p>9:00 AM - 11:00 AM<br>Monday to Friday</p>
                        </div>
                        <div class="benefit-item">
                            <h4>Afternoon Batch</h4>
                            <p>2:00 PM - 4:00 PM<br>Monday to Friday</p>
                        </div>
                        <div class="benefit-item">
                            <h4>Evening Batch</h4>
                            <p>6:00 PM - 8:00 PM<br>Monday to Friday</p>
                        </div>
                        <div class="benefit-item">
                            <h4>Weekend Batch</h4>
                            <p>10:00 AM - 2:00 PM<br>Saturday & Sunday</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PAGE 4: Admission Form -->
            <div class="page">
                <div class="page-header">
                    <div class="logo-container">
                        <img src="logo.png" alt="EduCasheer SiliconSpark Logo" class="logo-img">
                        <div>
                            <h1 class="page-title">Admission Form</h1>
                            <p class="page-subtitle">Complete this form to join EduCasheer SiliconSpark</p>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-user"></i>Personal Information</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Full Name *</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Date of Birth</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Pa</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Gender</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Phone Number(WhatsApp)*</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Aadhar Number</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">Address *</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-graduation-cap"></i>Educational Background</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Highest Qualification</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Current Status</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">Previous Computer Experience</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-laptop-code"></i>Course Selection</h2>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course1">
                        <label for="course1">Learn from Scratch (₹3,000 - 2 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course2">
                        <label for="course2">Learn to Code - Basic (₹6,000 - 3 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course3">
                        <label for="course3">Learn to Code - Advanced (₹10,000 - 4 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course4">
                        <label for="course4">Build the Web (₹12,000 - 4 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course5">
                        <label for="course5">Explore AI & Beyond (₹17,500 - 5 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course6">
                        <label for="course6">Computer Hardware (₹6,600 - 3 Months)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="course7">
                        <label for="course7">Complete Package (₹48,000 - 12 Months) - 15% Discount</label>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-clock"></i>Preferred Batch Timing</h2>
                    <div class="checkbox-group">
                        <input type="radio" name="batch" id="morning">
                        <label for="morning">Morning Batch (9:00 AM - 11:00 AM)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="radio" name="batch" id="afternoon">
                        <label for="afternoon">Afternoon Batch (2:00 PM - 4:00 PM)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="radio" name="batch" id="evening">
                        <label for="evening">Evening Batch (6:00 PM - 8:00 PM)</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="radio" name="batch" id="weekend">
                        <label for="weekend">Weekend Batch (10:00 AM - 2:00 PM)</label>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-money-bill"></i>Payment Information</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Payment Mode</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Total Amount</label>
                            <div style="border-bottom: 2px solid #333; height: 30px; margin-top: 5px;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title"><i class="fas fa-file-signature"></i>Declaration & Signature</h2>
                    <div class="checkbox-group">
                        <input type="checkbox" id="terms">
                        <label for="terms">I agree to the terms and conditions of EduCasheer SiliconSpark</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="updates">
                        <label for="updates">I agree to receive updates about courses and programs</label>
                    </div>

                    <div class="form-row" style="margin-top: 30px;">
                        <div class="form-group">
                            <label class="form-label">Student Signature</label>
                            <div style="border-bottom: 2px solid #4568dc; height: 40px; margin-top: 10px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Date</label>
                            <div style="border-bottom: 2px solid #4568dc; height: 40px; margin-top: 10px;"></div>
                        </div>
                    </div>

                    <div class="form-row" style="margin-top: 20px;">
                        <div class="form-group">
                            <label class="form-label">Parent/Guardian Signature (if under 18)</label>
                            <div style="border-bottom: 2px solid #4568dc; height: 40px; margin-top: 10px;"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Date</label>
                            <div style="border-bottom: 2px solid #4568dc; height: 40px; margin-top: 10px;"></div>
                        </div>
                    </div>
                </div>

                <div class="highlight-box" style="margin-top: 30px;">
                    <h3><i class="fas fa-info-circle"></i> For Office Use Only</h3>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 15px; text-align: left;">
                        <div>
                            <p><strong>Application No:</strong> ________________</p>
                            <p><strong>Received Date:</strong> ________________</p>
                            <p><strong>Batch Assigned:</strong> ________________</p>
                        </div>
                        <div>
                            <p><strong>Fee Received:</strong> ________________</p>
                            <p><strong>Receipt No:</strong> ________________</p>
                            <p><strong>Authorized Signature:</strong> ________________</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple script for form functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('EduCasheer SiliconSpark 4-Page Printable Form Loaded');
        });
    </script>
</body>
</html>
